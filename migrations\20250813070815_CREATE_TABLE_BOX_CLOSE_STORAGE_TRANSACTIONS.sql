-- migrate:up
CREATE TABLE storage_transactions (
    id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(255) NOT NULL,
    date_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    type VARCHAR(255) NOT NULL,
    msg VARCHAR(255) DEFAULT NULL,
    result VARCHAR(255) DEFAULT NULL,
    request TEXT DEFAULT NULL,
    response TEXT DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- migrate:down
DROP TABLE storage_transactions;
