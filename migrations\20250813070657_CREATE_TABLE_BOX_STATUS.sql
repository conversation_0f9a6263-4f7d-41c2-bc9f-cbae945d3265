-- migrate:up
CREATE TABLE box_status (
    id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(255) NOT NULL UNIQUE,
    box_uuid VARCHAR(255) DEFAULT NULL,
    date_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    ambient_temperature VARCHAR(255) DEFAULT NULL,
    box_temperature VARCHAR(255) DEFAULT NULL,
    humidity VARCHAR(255) DEFAULT NULL,
    compressor_run VARCHAR(255) DEFAULT NULL,
    heating_run VARCHAR(255) DEFAULT NULL,
    lighting_on VARCHAR(255) DEFAULT NULL,
    power_supply VARCHAR(255) DEFAULT NULL,
    box_open VARCHAR(255) DEFAULT NULL,
    mac VARCHAR(255) DEFAULT NULL,
    INDEX box_status_uuid_index (uuid),
    INDEX box_status_box_uuid_index (box_uuid),
    INDEX box_status_mac_index (mac)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- migrate:down
DROP TABLE box_status;
