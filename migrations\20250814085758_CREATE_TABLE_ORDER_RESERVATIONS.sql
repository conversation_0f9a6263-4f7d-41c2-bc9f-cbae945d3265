-- migrate:up
CREATE TABLE order_reservations (
    id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    box_uuid VARCHAR(255) DEFAULT NULL,
    section_id VARCHAR(255) DEFAULT NULL,
    status INT(11) NOT NULL DEFAULT 0,
    insert_pin VARCHAR(255) DEFAULT NULL,
    pickup_pin VARCHAR(255) DEFAULT NULL,
    phone_number VARCHAR(255) DEFAULT NULL,
    size_category INT(11) DEFAULT NULL,
    expired INT(11) DEFAULT NULL,
    type VARCHAR(255) DEFAULT NULL,
    last_update TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- migrate:down
DROP TABLE order_reservations;
