"""
MQTT Service module for integration with FastAPI application
"""
import logging
from typing import Dict, Any, Callable
from .client import mqtt_client, start_mqtt_client, stop_mqtt_client

logger = logging.getLogger(__name__)

class MQTTService:
    """Service class for managing MQTT functionality in the application"""
    
    def __init__(self):
        self.is_initialized = False
    
    async def initialize(self):
        """Initialize the MQTT service"""
        if self.is_initialized:
            logger.warning("MQTT service already initialized")
            return
            
        try:
            start_mqtt_client()
            self.is_initialized = True
            logger.info("MQTT service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize MQTT service: {e}")
            raise
    
    async def shutdown(self):
        """Shutdown the MQTT service"""
        if not self.is_initialized:
            return
            
        try:
            stop_mqtt_client()
            self.is_initialized = False
            logger.info("MQTT service shutdown successfully")
        except Exception as e:
            logger.error(f"Error during MQTT service shutdown: {e}")
    
    def add_message_handler(self, topic_pattern: str, handler: Callable[[str, Dict[str, Any]], None]):
        """Add a custom message handler"""
        mqtt_client.add_message_handler(topic_pattern, handler)
    
    def remove_message_handler(self, topic_pattern: str):
        """Remove a message handler"""
        mqtt_client.remove_message_handler(topic_pattern)
    
    def publish_response(self, command_topic: str, response: Dict[str, Any]):
        """Publish a response message to the corresponding response topic"""
        mqtt_client.publish_response(command_topic, response)

    def publish_general_response(self, response: Dict[str, Any]):
        """Publish a response message to the general response topic"""
        mqtt_client.publish_general_response(response)
    
    def is_connected(self) -> bool:
        """Check if MQTT client is connected"""
        return mqtt_client.is_client_connected()
    
    def get_status(self) -> Dict[str, Any]:
        """Get MQTT service status"""
        return {
            "initialized": self.is_initialized,
            "connected": self.is_connected(),
            "broker": mqtt_client.broker,
            "port": mqtt_client.port,
            "client_id": mqtt_client.client_id,
            "base_topic": mqtt_client.base_topic,
            "base_response_topic": mqtt_client.base_response_topic
        }

# Global MQTT service instance
mqtt_service = MQTTService()

async def start_mqtt_service():
    """Start the MQTT service"""
    await mqtt_service.initialize()

async def stop_mqtt_service():
    """Stop the MQTT service"""
    await mqtt_service.shutdown()

def get_mqtt_service() -> MQTTService:
    """Get the global MQTT service instance"""
    return mqtt_service
