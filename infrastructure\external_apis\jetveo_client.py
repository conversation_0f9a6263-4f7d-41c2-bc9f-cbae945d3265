"""
Jetveo API Client - External API integration for product data.
Handles communication with Jetveo API for product information, PIN validation, and heartbeat.

Features:
1. Product Data: Fetch product information by EAN, search products, get categories
2. PIN Validation: Validate operator PINs against Jetveo server
3. Heartbeat Service: Automatic "I'm alive" messages sent periodically

PIN Check Integration:
- The check_pin() method is ready to use for PIN validation
- To switch from MockResponse to real API in box/router.py:
  1. Uncomment: response_data = await jetveo_client.check_pin(pin, SERIAL_NUMBER or "unknown")
  2. Remove the MockResponse lines
  3. Ensure EXTERNAL_API_BASE_URL and EXTERNAL_API_TOKEN are properly configured

Heartbeat Service:
- Automatically started when the application starts (see main.py lifespan)
- Sends POST /api/imalive every 9 minutes by default
- Configurable via JETVEO_HEARTBEAT_INTERVAL_MINUTES environment variable
- Uses SERIAL_NUMBER from environment for device identification
"""

import logging
import aiohttp
import asyncio
from typing import Optional, Dict, Any
from os import getenv
from dotenv import load_dotenv
import threading

from sale.models import ExternalProduct, ExternalProductResponse

load_dotenv()

logger = logging.getLogger(__name__)

class JetveoClient:
    """Client for Jetveo external API"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.base_url = getenv("EXTERNAL_API_BASE_URL", "https://4a8fa4e1-8948-4b4c-9baa-4839d158ad96.eu.jetveo.io")
        self.partner_code = getenv("EXTERNAL_API_PARTNER_CODE", "drmax")
        self.api_token = getenv("EXTERNAL_API_TOKEN", "Gj1lojJdMIrgC13psFsWwveas8PYLdUC")
        self.timeout = int(getenv("EXTERNAL_API_TIMEOUT", "10"))
        self.heartbeat_interval = int(getenv("JETVEO_HEARTBEAT_INTERVAL_MINUTES", "9")) * 60  # Convert to seconds
        self.serial_number = getenv("SERIAL_NUMBER", "unknown")
        self._heartbeat_task = None
        self._heartbeat_running = False
    
    async def fetch_product_by_ean(self, ean: str) -> Optional[ExternalProduct]:
        """
        Fetch product information from external API by EAN code.
        
        Args:
            ean: EAN code to search for
            
        Returns:
            ExternalProduct object if found, None otherwise
        """
        try:
            url = f"{self.base_url}/api/product"
            params = {
                "offset": 0,
                "limit": 100,  # Search in larger batch to increase chance of finding EAN
                "partner-code": self.partner_code
            }
            
            headers = {
                "jv-api-key": self.api_token
            }
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Parse response using Pydantic model
                        external_response = ExternalProductResponse(**data)
                        
                        if external_response.success:
                            # Search for product with matching EAN
                            for product in external_response.items:
                                if product.ean == ean:
                                    self.logger.info(f"Found product for EAN {ean}: {product.text.cs.name}")
                                    return product
                            
                            self.logger.warning(f"Product with EAN {ean} not found in external API")
                            return None
                        else:
                            self.logger.error(f"External API returned unsuccessful response: {external_response.message}")
                            return None
                    else:
                        self.logger.error(f"External API request failed with status {response.status}")
                        return None
                        
        except asyncio.TimeoutError:
            self.logger.error(f"External API request timed out for EAN {ean}")
            return None
        except Exception as e:
            self.logger.error(f"Error fetching product from external API: {e}")
            return None
    
    async def search_products(self, query: str, limit: int = 100) -> Optional[ExternalProductResponse]:
        """
        Search products in external API.
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            ExternalProductResponse object or None if failed
        """
        try:
            url = f"{self.base_url}/api/product"
            params = {
                "offset": 0,
                "limit": limit,
                "partner-code": self.partner_code,
                "q": query
            }
            
            headers = {
                "jv-api-key": self.api_token
            }
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return ExternalProductResponse(**data)
                    else:
                        self.logger.error(f"External API search failed with status {response.status}")
                        return None
                        
        except asyncio.TimeoutError:
            self.logger.error(f"External API search timed out for query: {query}")
            return None
        except Exception as e:
            self.logger.error(f"Error searching products in external API: {e}")
            return None
    
    async def get_product_categories(self) -> Optional[Dict[str, Any]]:
        """
        Get product categories from external API.
        
        Returns:
            Categories data or None if failed
        """
        try:
            url = f"{self.base_url}/api/categories"
            params = {
                "partner-code": self.partner_code
            }
            
            headers = {
                "jv-api-key": self.api_token
            }
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        self.logger.error(f"External API categories request failed with status {response.status}")
                        return None
                        
        except asyncio.TimeoutError:
            self.logger.error("External API categories request timed out")
            return None
        except Exception as e:
            self.logger.error(f"Error fetching categories from external API: {e}")
            return None
    
    async def check_pin(self, pin: str, serial_number: str) -> Optional[Dict[str, Any]]:
        """
        Check PIN validity with external API.

        Args:
            pin: PIN code to validate
            serial_number: Device serial number

        Returns:
            Dict with PIN check result or None if failed
            Expected response format:
            {
                "serial_number": "xxxx",
                "status": "allow" | "deny",
                "operator_id": int,
                "name": "Operator Name",
                "type": "1" | "2" | etc.
            }
        """
        try:
            url = f"{self.base_url}/pin_check"

            # Prepare request data
            request_data = {
                "serial_number": serial_number,
                "scanned_pin": pin
            }

            headers = {
                "jv-api-key": self.api_token,
                "Content-Type": "application/json"
            }

            timeout = aiohttp.ClientTimeout(total=self.timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=request_data, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.logger.info(f"PIN check successful for PIN {pin}")
                        return data
                    else:
                        self.logger.error(f"PIN check failed with status {response.status}")
                        return None

        except asyncio.TimeoutError:
            self.logger.error(f"PIN check request timed out for PIN {pin}")
            return None
        except Exception as e:
            self.logger.error(f"Error checking PIN with external API: {e}")
            return None

    async def send_heartbeat(self) -> bool:
        """
        Send "I'm alive" heartbeat to Jetveo server.

        Returns:
            True if heartbeat sent successfully, False otherwise
        """
        try:
            url = f"{self.base_url}/api/imalive"

            request_data = {
                "SerialNumber": self.serial_number
            }

            headers = {
                "jv-api-key": self.api_token,
                "Content-Type": "application/json"
            }

            timeout = aiohttp.ClientTimeout(total=self.timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=request_data, headers=headers) as response:
                    if response.status == 200:
                        self.logger.info(f"Heartbeat sent successfully for device {self.serial_number}")
                        return True
                    else:
                        self.logger.warning(f"Heartbeat failed with status {response.status}")
                        return False

        except asyncio.TimeoutError:
            self.logger.error("Heartbeat request timed out")
            return False
        except Exception as e:
            self.logger.error(f"Error sending heartbeat: {e}")
            return False

    async def _heartbeat_loop(self):
        """
        Internal heartbeat loop that runs periodically.
        """
        self.logger.info(f"Starting heartbeat loop with interval {self.heartbeat_interval} seconds")

        while self._heartbeat_running:
            try:
                await self.send_heartbeat()
                await asyncio.sleep(self.heartbeat_interval)
            except asyncio.CancelledError:
                self.logger.info("Heartbeat loop cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in heartbeat loop: {e}")
                # Wait a bit before retrying
                await asyncio.sleep(60)

    def start_heartbeat(self):
        """
        Start the periodic heartbeat in the background.
        """
        if self._heartbeat_running:
            self.logger.warning("Heartbeat is already running")
            return

        self._heartbeat_running = True

        # Create new event loop for the heartbeat if we're not in an async context
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # We're in an async context, create task
                self._heartbeat_task = loop.create_task(self._heartbeat_loop())
            else:
                # Start in a separate thread
                def run_heartbeat():
                    asyncio.run(self._heartbeat_loop())

                heartbeat_thread = threading.Thread(target=run_heartbeat, daemon=True)
                heartbeat_thread.start()

        except RuntimeError:
            # No event loop, start in a separate thread
            def run_heartbeat():
                asyncio.run(self._heartbeat_loop())

            heartbeat_thread = threading.Thread(target=run_heartbeat, daemon=True)
            heartbeat_thread.start()

        self.logger.info("Heartbeat started")

    def stop_heartbeat(self):
        """
        Stop the periodic heartbeat.
        """
        if not self._heartbeat_running:
            return

        self._heartbeat_running = False

        if self._heartbeat_task and not self._heartbeat_task.done():
            self._heartbeat_task.cancel()

        self.logger.info("Heartbeat stopped")

    async def test_connection(self) -> bool:
        """
        Test connection to external API.

        Returns:
            True if connection successful, False otherwise
        """
        try:
            url = f"{self.base_url}/api/product"
            params = {
                "offset": 0,
                "limit": 1,
                "partner-code": self.partner_code
            }

            headers = {
                "jv-api-key": self.api_token
            }

            timeout = aiohttp.ClientTimeout(total=self.timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, params=params, headers=headers) as response:
                    return response.status == 200

        except Exception as e:
            self.logger.error(f"External API connection test failed: {e}")
            return False

# Global client instance
jetveo_client = JetveoClient()

def start_jetveo_heartbeat():
    """
    Start the Jetveo heartbeat service.
    Call this function during application startup.
    """
    jetveo_client.start_heartbeat()

def stop_jetveo_heartbeat():
    """
    Stop the Jetveo heartbeat service.
    Call this function during application shutdown.
    """
    jetveo_client.stop_heartbeat()

# jetveo_client.fetch_product_by_ean("1234567890123")