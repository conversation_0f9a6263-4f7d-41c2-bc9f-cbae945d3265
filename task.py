"""
Task 1:
    Create order functionality. Create folder "order" at same level as "storage" folder. Then create folder "order" in "domains" folder. (at same level as "domains/storage"). For this task inspire with files from folders "storage" and "domains/storage". expecially with fastapi and websocket communication. For this moment do it without timeline logging. Create new endpoints:

    order_reservation "type" column types:
        employee_send - order, which employee sends to courier
        employee_deliver - order, which courier delivers to customer



    /order/employment/courier/pickup-expired       # request to pickup expired orders

        request body:
            {
            "operatod_id": 1    # operator id, who is picking up the orders
            }

        response body:
            {
                "session_id": "1a219d3e-ed9b-411a-92c3-96c11b0bfda5",       # create new websocket session and returns session id
                "success": true,        # if any error occures, returns false
                "message": "Expired orders found",    # success or error message
                "sections": [
                    1,4,6               # all sections with expired orders (retusns all records in order_reservation table section_ids, where expired==0 and status==1 and type=="employee_send")
                ]
                "total_sections": 3      # total number of sections with expired orders
            }

        functionality:
            - if total_sections is greather than 0, it creates session_id, then starts pickup loop
            - employment_pickup_loop(sections)
            - close websocket session



    /order/employment/courier/pickup        # function for courier to pickup orders from employees
        request body:
            {
            "operatod_id": 1    # operator id, who is picking up the orders
            }

        response body:
            {
                "session_id": "1a219d3e-ed9b-411a-92c3-96c11b0bfda5",       # if total_sections is greather than 0, create new websocket session and returns session id
                "success": true,        # if any error occures or phone number is not valid, returns false
                "message": ""           # success or error message
                "sections": [
                    1,4,6               # all sections with employee's orders (returns all records in order_reservation table section_ids, where expired==0 and status==1 and type=="employee_send")
                ]
                "total_sections": 3     # total number of sections with employee's orders
            }

        functionality:
            - similar to /order/pickup-expired
            - employment_pickup_loop(sections)
            - close websocket session
                




    /order/employment/courier/deliver   # functino to deliver orders to employees, this function is for courier
        request body:
            {
                "phone_number": "123456789"    # employee's phone number
            }

        response body:
            {
                "session_id": "1a219d3e-ed9b-411a-92c3-96c11b0bfda5",       # if phone number is valid, create new websocket session and returns session id
                "success": true,        # if any error occures or phone number is not valid, returns false
                "message": ""           # success or error message
            }
            
        functionality:
            - after request, it sends request to jetveo server to "/api/employment/deliver" to check if phone number is valid,
              request: {"phone_number": "123456789"}, response: {"phone_number": "123456789","valid": true, "section_id": null/1}
            - if number is valid, it calls function select_section():
                success, selected_section_id = select_section()
                    - if success==true, it creates new record in order_reservation and saves phone_number and selected_section_id and "employee_deliver" into type column. Then sends message {"order_deliver": true}.
                    - if success==false, it just sends {"order_deliver": false, "message": some_error_message}
            - closes websocket session





    /order/employment/customer/send 

        request body:
            {
                "phone_number": "123456789"    # employee's phone number
            }

        response body:
            {
                "session_id": "1a219d3e-ed9b-411a-92c3-96c11b0bfda5",       # create new websocket session and returns session id
                "success": true,        # if any error occures, returns false
                "section_id": 1,        # reserved section id, if not reserved, returns "section_id": null
                "valid": true,          # if phone number is valid, returns true, else returns false
                "message": "Employee notified successfully"    # success or error message
            }

        functionality:
            - after request, it sends request to jetveo server to "/api/employment/send" to check if phone number is valid, request: {"phone_number": "123456789"}, response: {"phone_number": "123456789","valid": true}
            - if phone nubmer is valid, it creates websocket session.
            - calls function select_section():
                success, selected_section_id = select_section()
                    - if success==true, it creates new record in order_reservation and saves phone_number and selected_section_id. Then sends message {"order_send": true}
                    - if success==false, it just sends {"order_send": false, "message": some_error_message}
            - closes websocket session



            

    /order/employment/customer/pickup.  ...  # similar to /product/pickup

        request body:
            {
                "pickup_pin": "123456"    # pickup pin
            }

        response body:
            - similar to /product /pickup

        functionality:
            - similar to /product/pickup, it created websockert session and waits for message {"type": "hardware_screen_ready"}
            - for valid pin, check if there is record in order_reservation with matching pin and status==1 and expired==0. check pickup_pin column


            







    USED FUNCITON BASIC LOGIC

    employment_pickup_loop(sections: List[int])
        picking_up = true
        while picking_up:
            message = wait_for_message()   # wait for message from websocket. {"type":"open_section", "section_id": 1} or {"type": "storno"}
            if message["type"] == "open_section":
                open_door(message["section_id"])
                while check_door_state(message["section_id"]) == 1:     # while door is opened
                    wait
                send_message({"section_id": message["section_id"], "status": "closed"})
            elif message["type"] == "storno":
                picking_up = false



    def select_section():       # function to choose section, returns success (if section was succesfully selected), section_id (if section was succesfully selected) and error_message (if section was not succesfully selected)

        selected_section = None
        selecting = True
        while selecting
            message = wait_for_message()   # wait for message from websocket. {"type":"open_section", "section_id": 1} or {"type": "selection_failed"}

            if message["type"] == "open_section":
                open_door(message["section_id"])
                send_message({"section_id": message["section_id"], "status": "opened"})
                while check_door_state(message["section_id"]) == 1:     # while door is opened
                    wait
                send_message({"section_id": message["section_id"], "status": "closed"})
                message = wait_for_message()        # {"inserted": true}
                if message["inserted"]:
                    selecting = False
                    selected_section = message["section_id"]
                    return true, selected_section
                elif not message["inserted"]:
                    send_message = {"message": "waiting for new message"}
            
            elif message["type"] == "selection_failed":
                selecting = False
                return false, null
    



            



TASK 2:
    add "is_available": true/false to each section in response, which returns endpoint /box/sections.
    You need to list tables order_reservations, sale_reservations, storage_reservations. Check only records where status==1.
    If in any of the tables there is record with section_id with status==1, section is not available. Section with identification_name=="stock" is always available

    
TASK 3:
    delete column "max_days" and its funcitonality in table storage_reservations and sale reservations. Both in code and in "migrations/" folder

    
TASK 4
    delete BOX_STATUS table. Both from code and from "migrations/" folder


all table structures you can find in migrations folder.
"""